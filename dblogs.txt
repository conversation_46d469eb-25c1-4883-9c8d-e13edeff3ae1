The files belonging to this database system will be owned by user "postgres".
This user must also own the server process.

The database cluster will be initialized with locale "en_US.utf8".
The default database encoding has accordingly been set to "UTF8".
The default text search configuration will be set to "english".

Data page checksums are disabled.

fixing permissions on existing directory /var/lib/postgresql/data ... ok
creating subdirectories ... ok
selecting dynamic shared memory implementation ... posix
selecting default max_connections ... 100
selecting default shared_buffers ... 128MB
selecting default time zone ... UTC
creating configuration files ... ok
running bootstrap script ... ok
performing post-bootstrap initialization ... ok
syncing data to disk ... ok


Success. You can now start the database server using:

    pg_ctl -D /var/lib/postgresql/data -l logfile start

waiting for server to start....2025-06-17 13:58:35.602 UTC [41] LOG:  starting PostgreSQL 15.13 on x86_64-pc-linux-musl, compiled by gcc (Alpine 14.2.0) 14.2.0, 64-bit
2025-06-17 13:58:35.607 UTC [41] LOG:  listening on Unix socket "/var/run/postgresql/.s.PGSQL.5432"
2025-06-17 13:58:35.620 UTC [44] LOG:  database system was shut down at 2025-06-17 13:58:33 UTC
2025-06-17 13:58:35.626 UTC [41] LOG:  database system is ready to accept connections
 done
server started
CREATE DATABASE


/usr/local/bin/docker-entrypoint.sh: running /docker-entrypoint-initdb.d/init.sql
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE


waiting for server to shut down...2025-06-17 13:58:35.822 UTC [41] LOG:  received fast shutdown request
.2025-06-17 13:58:35.827 UTC [41] LOG:  aborting any active transactions
2025-06-17 13:58:35.828 UTC [41] LOG:  background worker "logical replication launcher" (PID 47) exited with exit code 1
2025-06-17 13:58:35.828 UTC [42] LOG:  shutting down
2025-06-17 13:58:35.833 UTC [42] LOG:  checkpoint starting: shutdown immediate
2025-06-17 13:58:36.511 UTC [42] LOG:  checkpoint complete: wrote 938 buffers (5.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.022 s, sync=0.641 s, total=0.683 s; sync files=321, longest=0.009 s, average=0.002 s; distance=4346 kB, estimate=4346 kB
2025-06-17 13:58:36.517 UTC [41] LOG:  database system is shut down
 done
server stopped

PostgreSQL init process complete; ready for start up.

